import * as fs from "fs/promises"
import * as path from "path"
import * as os from "os"
import { TempFileChangeManager } from "../services/TempFileChangeManager"

describe("TempFileChangeManager", () => {
	let tempDir: string
	let manager: TempFileChangeManager

	beforeEach(async () => {
		// Create a temporary directory for testing
		tempDir = await fs.mkdtemp(path.join(os.tmpdir(), "cubent-test-"))
		
		// Reset the singleton instance
		TempFileChangeManager.reset()
		
		// Create a new instance
		manager = TempFileChangeManager.getInstance(tempDir)
	})

	afterEach(async () => {
		// Clean up temporary directory
		try {
			await fs.rm(tempDir, { recursive: true, force: true })
		} catch (error) {
			console.warn("Failed to clean up temp directory:", error)
		}
		
		// Reset the singleton instance
		TempFileChangeManager.reset()
	})

	test("should initialize correctly", () => {
		expect(manager).toBeDefined()
		expect(manager.hasChanges()).toBe(false)
		expect(manager.getAllChanges()).toEqual([])
	})

	test("should add file changes", async () => {
		const filePath = "test.txt"
		const originalContent = "Hello World"
		const modifiedContent = "Hello Modified World"

		await manager.addFileChange(filePath, originalContent, modifiedContent, "modify")

		expect(manager.hasChanges()).toBe(true)
		expect(manager.getAllChanges()).toHaveLength(1)

		const change = manager.getFileChange(filePath)
		expect(change).toBeDefined()
		expect(change?.filePath).toBe(filePath)
		expect(change?.originalContent).toBe(originalContent)
		expect(change?.modifiedContent).toBe(modifiedContent)
		expect(change?.type).toBe("modify")
	})

	test("should calculate line changes correctly", async () => {
		const filePath = "test.txt"
		const originalContent = "Line 1\nLine 2\nLine 3"
		const modifiedContent = "Line 1\nModified Line 2\nLine 3\nNew Line 4"

		await manager.addFileChange(filePath, originalContent, modifiedContent, "modify")

		const stats = manager.getTotalStats()
		expect(stats.fileCount).toBe(1)
		expect(stats.totalLinesAdded).toBeGreaterThan(0)
		expect(stats.totalLinesRemoved).toBeGreaterThan(0)
	})

	test("should get file preview data", async () => {
		const filePath = "test.txt"
		const originalContent = "Hello World"
		const modifiedContent = "Hello Modified World"

		await manager.addFileChange(filePath, originalContent, modifiedContent, "modify")

		const preview = manager.getFilePreview(filePath)
		expect(preview).toBeDefined()
		expect(preview?.filePath).toBe(filePath)
		expect(preview?.originalContent).toBe(originalContent)
		expect(preview?.modifiedContent).toBe(modifiedContent)
		expect(preview?.type).toBe("modify")
	})

	test("should commit changes to disk", async () => {
		const filePath = "test.txt"
		const originalContent = "Hello World"
		const modifiedContent = "Hello Modified World"

		await manager.addFileChange(filePath, originalContent, modifiedContent, "create")

		const result = await manager.commitAllChanges()
		expect(result.success).toBe(true)
		expect(result.errors).toEqual([])

		// Verify file was created
		const absolutePath = path.resolve(tempDir, filePath)
		const fileContent = await fs.readFile(absolutePath, "utf-8")
		expect(fileContent).toBe(modifiedContent)

		// Verify changes were cleared
		expect(manager.hasChanges()).toBe(false)
	})

	test("should discard all changes", async () => {
		const filePath = "test.txt"
		const originalContent = "Hello World"
		const modifiedContent = "Hello Modified World"

		await manager.addFileChange(filePath, originalContent, modifiedContent, "modify")
		expect(manager.hasChanges()).toBe(true)

		await manager.discardAllChanges()
		expect(manager.hasChanges()).toBe(false)
		expect(manager.getAllChanges()).toEqual([])
	})

	test("should remove specific file changes", async () => {
		const filePath1 = "test1.txt"
		const filePath2 = "test2.txt"
		const originalContent = "Hello World"
		const modifiedContent = "Hello Modified World"

		await manager.addFileChange(filePath1, originalContent, modifiedContent, "modify")
		await manager.addFileChange(filePath2, originalContent, modifiedContent, "modify")

		expect(manager.getAllChanges()).toHaveLength(2)

		const removed = manager.removeFileChange(filePath1)
		expect(removed).toBe(true)
		expect(manager.getAllChanges()).toHaveLength(1)
		expect(manager.getFileChange(filePath1)).toBeUndefined()
		expect(manager.getFileChange(filePath2)).toBeDefined()
	})

	test("should handle singleton pattern correctly", () => {
		const instance1 = TempFileChangeManager.getInstance()
		const instance2 = TempFileChangeManager.getInstance()
		
		expect(instance1).toBe(instance2)
		expect(instance1).toBe(manager)
	})
})
