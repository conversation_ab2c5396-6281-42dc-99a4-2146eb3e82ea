import { useState, useEffect } from "react"
import { vscode } from "@src/utils/vscode"

interface FilePreviewProps {
	filePath: string
	isOpen: boolean
	onClose: () => void
}

interface DiffLine {
	type: "context" | "added" | "removed"
	content: string
	oldLineNumber?: number
	newLineNumber?: number
}

interface FilePreviewData {
	filePath: string
	originalContent: string
	modifiedContent: string
	diffContent: string
	type: "create" | "modify"
	linesAdded: number
	linesRemoved: number
}

const FilePreview: React.FC<FilePreviewProps> = ({ filePath, isOpen, onClose }) => {
	const [previewData, setPreviewData] = useState<FilePreviewData | null>(null)
	const [diffLines, setDiffLines] = useState<DiffLine[]>([])
	const [loading, setLoading] = useState(false)
	const [viewMode, setViewMode] = useState<"diff" | "original" | "modified">("diff")

	useEffect(() => {
		if (isOpen && filePath) {
			fetchPreviewData()
		}
	}, [isOpen, filePath])

	const fetchPreviewData = async () => {
		setLoading(true)
		try {
			// Request preview data from extension
			vscode.postMessage({ type: "getFilePreview", text: filePath })
		} catch (error) {
			console.error("Error fetching preview data:", error)
		} finally {
			setLoading(false)
		}
	}

	// Listen for preview data response
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data
			if (message.type === "filePreviewData" && message.filePath === filePath) {
				setPreviewData(message.data)
				if (message.data) {
					parseDiffContent(message.data.diffContent)
				}
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [filePath])

	const parseDiffContent = (diffContent: string) => {
		const lines = diffContent.split("\n")
		const parsedLines: DiffLine[] = []
		let oldLineNumber = 1
		let newLineNumber = 1

		for (const line of lines) {
			if (line.startsWith("@@")) {
				// Parse hunk header to get line numbers
				const match = line.match(/@@ -(\d+),?\d* \+(\d+),?\d* @@/)
				if (match) {
					oldLineNumber = parseInt(match[1])
					newLineNumber = parseInt(match[2])
				}
				continue
			}

			if (line.startsWith("+++") || line.startsWith("---") || line.startsWith("diff")) {
				continue
			}

			if (line.startsWith("+")) {
				parsedLines.push({
					type: "added",
					content: line.substring(1),
					newLineNumber: newLineNumber++,
				})
			} else if (line.startsWith("-")) {
				parsedLines.push({
					type: "removed",
					content: line.substring(1),
					oldLineNumber: oldLineNumber++,
				})
			} else {
				parsedLines.push({
					type: "context",
					content: line.startsWith(" ") ? line.substring(1) : line,
					oldLineNumber: oldLineNumber++,
					newLineNumber: newLineNumber++,
				})
			}
		}

		setDiffLines(parsedLines)
	}

	const renderContent = () => {
		if (!previewData) return null

		switch (viewMode) {
			case "original":
				return (
					<pre className="text-xs text-vscode-foreground whitespace-pre-wrap font-mono">
						{previewData.originalContent}
					</pre>
				)
			case "modified":
				return (
					<pre className="text-xs text-vscode-foreground whitespace-pre-wrap font-mono">
						{previewData.modifiedContent}
					</pre>
				)
			case "diff":
			default:
				return (
					<div className="font-mono text-xs">
						{diffLines.map((line, index) => (
							<div
								key={index}
								className={`flex ${
									line.type === "added"
										? "bg-green-500/10 text-green-400"
										: line.type === "removed"
										? "bg-red-500/10 text-red-400"
										: "text-vscode-foreground"
								}`}>
								<div className="w-12 text-right pr-2 text-vscode-descriptionForeground border-r border-vscode-input-border/30">
									{line.oldLineNumber || ""}
								</div>
								<div className="w-12 text-right pr-2 text-vscode-descriptionForeground border-r border-vscode-input-border/30">
									{line.newLineNumber || ""}
								</div>
								<div className="flex-1 pl-2">
									{line.type === "added" && <span className="text-green-400">+</span>}
									{line.type === "removed" && <span className="text-red-400">-</span>}
									{line.type === "context" && <span className="text-vscode-descriptionForeground"> </span>}
									<span className="ml-1">{line.content}</span>
								</div>
							</div>
						))}
					</div>
				)
		}
	}

	if (!isOpen) return null

	return (
		<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
			<div className="bg-vscode-editor-background border border-vscode-input-border rounded-lg w-[90%] h-[80%] flex flex-col">
				{/* Header */}
				<div className="flex items-center justify-between p-4 border-b border-vscode-input-border/30">
					<div className="flex items-center gap-4">
						<h3 className="text-vscode-foreground font-medium">
							{filePath.split("/").pop()} - Preview Changes
						</h3>
						{previewData && (
							<div className="flex items-center gap-2 text-xs">
								<span className="text-green-400">+{previewData.linesAdded}</span>
								<span className="text-red-400">-{previewData.linesRemoved}</span>
							</div>
						)}
					</div>
					<div className="flex items-center gap-2">
						{/* View mode selector */}
						<div className="flex bg-vscode-input-background rounded">
							<button
								onClick={() => setViewMode("diff")}
								className={`px-3 py-1 text-xs rounded-l ${
									viewMode === "diff"
										? "bg-vscode-button-background text-white"
										: "text-vscode-foreground hover:bg-vscode-toolbar-hoverBackground/40"
								}`}>
								Diff
							</button>
							<button
								onClick={() => setViewMode("original")}
								className={`px-3 py-1 text-xs ${
									viewMode === "original"
										? "bg-vscode-button-background text-white"
										: "text-vscode-foreground hover:bg-vscode-toolbar-hoverBackground/40"
								}`}>
								Original
							</button>
							<button
								onClick={() => setViewMode("modified")}
								className={`px-3 py-1 text-xs rounded-r ${
									viewMode === "modified"
										? "bg-vscode-button-background text-white"
										: "text-vscode-foreground hover:bg-vscode-toolbar-hoverBackground/40"
								}`}>
								Modified
							</button>
						</div>
						<button
							onClick={onClose}
							className="p-1 text-vscode-foreground hover:opacity-70 transition-opacity">
							<span className="codicon codicon-close text-sm"></span>
						</button>
					</div>
				</div>

				{/* Content */}
				<div className="flex-1 overflow-auto p-4">
					{loading ? (
						<div className="flex items-center justify-center h-full">
							<div className="text-vscode-foreground">Loading preview...</div>
						</div>
					) : (
						renderContent()
					)}
				</div>
			</div>
		</div>
	)
}

export default FilePreview
