import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs/promises"
import * as diff from "diff"

export interface TempFileChange {
	filePath: string
	originalContent: string
	modifiedContent: string
	diffContent: string
	type: "create" | "modify"
	timestamp: number
	linesAdded: number
	linesRemoved: number
}

export interface FilePreviewData {
	filePath: string
	originalContent: string
	modifiedContent: string
	diffContent: string
	type: "create" | "modify"
	linesAdded: number
	linesRemoved: number
}

/**
 * Manages temporary file changes before they are committed to disk
 * Acts like a staging area similar to git's index
 */
export class TempFileChangeManager {
	private static instance: TempFileChangeManager | undefined
	private tempChanges = new Map<string, TempFileChange>()
	private backupDir: string
	private cwd: string

	private constructor(cwd: string) {
		this.cwd = cwd
		this.backupDir = path.join(cwd, ".cubent", "temp-changes")
	}

	public static getInstance(cwd?: string): TempFileChangeManager {
		if (!TempFileChangeManager.instance && cwd) {
			TempFileChangeManager.instance = new TempFileChangeManager(cwd)
		}
		if (!TempFileChangeManager.instance) {
			throw new Error("TempFileChangeManager not initialized. Call getInstance with cwd first.")
		}
		return TempFileChangeManager.instance
	}

	public static reset(): void {
		TempFileChangeManager.instance = undefined
	}

	/**
	 * Initialize the backup directory
	 */
	private async ensureBackupDir(): Promise<void> {
		try {
			await fs.mkdir(this.backupDir, { recursive: true })
		} catch (error) {
			console.error("Failed to create backup directory:", error)
		}
	}

	/**
	 * Add a file change to temporary storage
	 */
	public async addFileChange(
		filePath: string,
		originalContent: string,
		modifiedContent: string,
		type: "create" | "modify"
	): Promise<void> {
		await this.ensureBackupDir()

		// Calculate diff
		const diffContent = this.calculateDiff(originalContent, modifiedContent, filePath)
		const { linesAdded, linesRemoved } = this.calculateLineChanges(diffContent)

		// Create backup of original content
		const backupPath = path.join(this.backupDir, `${path.basename(filePath)}.${Date.now()}.backup`)
		try {
			await fs.writeFile(backupPath, originalContent, "utf-8")
		} catch (error) {
			console.error("Failed to create backup:", error)
		}

		const change: TempFileChange = {
			filePath,
			originalContent,
			modifiedContent,
			diffContent,
			type,
			timestamp: Date.now(),
			linesAdded,
			linesRemoved,
		}

		this.tempChanges.set(filePath, change)
	}

	/**
	 * Get all temporary changes
	 */
	public getAllChanges(): TempFileChange[] {
		return Array.from(this.tempChanges.values())
	}

	/**
	 * Get a specific file change
	 */
	public getFileChange(filePath: string): TempFileChange | undefined {
		return this.tempChanges.get(filePath)
	}

	/**
	 * Get preview data for a specific file
	 */
	public getFilePreview(filePath: string): FilePreviewData | undefined {
		const change = this.tempChanges.get(filePath)
		if (!change) return undefined

		return {
			filePath: change.filePath,
			originalContent: change.originalContent,
			modifiedContent: change.modifiedContent,
			diffContent: change.diffContent,
			type: change.type,
			linesAdded: change.linesAdded,
			linesRemoved: change.linesRemoved,
		}
	}

	/**
	 * Remove a specific file change
	 */
	public removeFileChange(filePath: string): boolean {
		return this.tempChanges.delete(filePath)
	}

	/**
	 * Check if there are any temporary changes
	 */
	public hasChanges(): boolean {
		return this.tempChanges.size > 0
	}

	/**
	 * Get total line statistics
	 */
	public getTotalStats(): { totalLinesAdded: number; totalLinesRemoved: number; fileCount: number } {
		let totalLinesAdded = 0
		let totalLinesRemoved = 0

		for (const change of this.tempChanges.values()) {
			totalLinesAdded += change.linesAdded
			totalLinesRemoved += change.linesRemoved
		}

		return {
			totalLinesAdded,
			totalLinesRemoved,
			fileCount: this.tempChanges.size,
		}
	}

	/**
	 * Commit all temporary changes to disk
	 */
	public async commitAllChanges(): Promise<{ success: boolean; errors: string[] }> {
		const errors: string[] = []

		for (const change of this.tempChanges.values()) {
			try {
				const absolutePath = path.resolve(this.cwd, change.filePath)
				
				// Ensure directory exists
				await fs.mkdir(path.dirname(absolutePath), { recursive: true })
				
				// Write the modified content to disk
				await fs.writeFile(absolutePath, change.modifiedContent, "utf-8")
			} catch (error) {
				const errorMsg = error instanceof Error ? error.message : String(error)
				errors.push(`Failed to write ${change.filePath}: ${errorMsg}`)
			}
		}

		if (errors.length === 0) {
			// Clear all temporary changes after successful commit
			this.tempChanges.clear()
			await this.cleanupBackups()
		}

		return {
			success: errors.length === 0,
			errors,
		}
	}

	/**
	 * Discard all temporary changes
	 */
	public async discardAllChanges(): Promise<void> {
		this.tempChanges.clear()
		await this.cleanupBackups()
	}

	/**
	 * Calculate diff between original and modified content
	 */
	private calculateDiff(originalContent: string, modifiedContent: string, filePath: string): string {
		const patch = diff.createPatch(filePath, originalContent, modifiedContent, "original", "modified")
		return patch
	}

	/**
	 * Calculate line changes from diff content
	 */
	private calculateLineChanges(diffContent: string): { linesAdded: number; linesRemoved: number } {
		let linesAdded = 0
		let linesRemoved = 0

		const lines = diffContent.split("\n")
		for (const line of lines) {
			if (line.startsWith("+") && !line.startsWith("+++")) {
				linesAdded++
			} else if (line.startsWith("-") && !line.startsWith("---")) {
				linesRemoved++
			}
		}

		return { linesAdded, linesRemoved }
	}

	/**
	 * Clean up backup files
	 */
	private async cleanupBackups(): Promise<void> {
		try {
			const files = await fs.readdir(this.backupDir)
			for (const file of files) {
				if (file.endsWith(".backup")) {
					await fs.unlink(path.join(this.backupDir, file))
				}
			}
		} catch (error) {
			console.error("Failed to cleanup backups:", error)
		}
	}
}
