import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs/promises"
import { exec } from "child_process"
import { promisify } from "util"

const execAsync = promisify(exec)

export interface GitFileChange {
	filePath: string
	status: string // M, A, D, R, etc.
	linesAdded: number
	linesRemoved: number
	isStaged: boolean
}

export interface FilePreviewData {
	filePath: string
	originalContent: string
	modifiedContent: string
	diffContent: string
	status: string
	linesAdded: number
	linesRemoved: number
}

/**
 * Git-based staging manager that uses actual git commands
 * Provides source control functionality for file changes
 */
export class GitStagingManager {
	private static instance: GitStagingManager | undefined
	private cwd: string

	private constructor(cwd: string) {
		this.cwd = cwd
	}

	public static getInstance(cwd?: string): GitStagingManager {
		if (!GitStagingManager.instance && cwd) {
			GitStagingManager.instance = new GitStagingManager(cwd)
		}
		if (!GitStagingManager.instance) {
			throw new Error("GitStagingManager not initialized. Call getInstance with cwd first.")
		}
		return GitStagingManager.instance
	}

	public static reset(): void {
		GitStagingManager.instance = undefined
	}

	/**
	 * Check if current directory is a git repository
	 */
	private async isGitRepo(): Promise<boolean> {
		try {
			await execAsync("git rev-parse --git-dir", { cwd: this.cwd })
			return true
		} catch (error) {
			return false
		}
	}

	/**
	 * Stage a file for commit
	 */
	public async stageFile(filePath: string): Promise<void> {
		if (!(await this.isGitRepo())) {
			throw new Error("Not a git repository")
		}

		try {
			await execAsync(`git add "${filePath}"`, { cwd: this.cwd })
		} catch (error) {
			throw new Error(`Failed to stage file ${filePath}: ${error}`)
		}
	}

	/**
	 * Unstage a file
	 */
	public async unstageFile(filePath: string): Promise<void> {
		if (!(await this.isGitRepo())) {
			throw new Error("Not a git repository")
		}

		try {
			await execAsync(`git reset HEAD "${filePath}"`, { cwd: this.cwd })
		} catch (error) {
			throw new Error(`Failed to unstage file ${filePath}: ${error}`)
		}
	}

	/**
	 * Get all changed files (staged and unstaged)
	 */
	public async getAllChanges(): Promise<GitFileChange[]> {
		if (!(await this.isGitRepo())) {
			return []
		}

		try {
			const { stdout } = await execAsync("git status --porcelain", { cwd: this.cwd })
			const changes: GitFileChange[] = []

			for (const line of stdout.split("\n")) {
				if (!line.trim()) continue

				const status = line.substring(0, 2)
				const filePath = line.substring(3)
				const isStaged = status[0] !== " " && status[0] !== "?"
				
				// Get line counts for this file
				const { linesAdded, linesRemoved } = await this.getLineChanges(filePath, isStaged)

				changes.push({
					filePath,
					status: status.trim(),
					linesAdded,
					linesRemoved,
					isStaged,
				})
			}

			return changes
		} catch (error) {
			console.error("Failed to get git changes:", error)
			return []
		}
	}

	/**
	 * Get line changes for a specific file
	 */
	private async getLineChanges(filePath: string, isStaged: boolean): Promise<{ linesAdded: number; linesRemoved: number }> {
		try {
			const command = isStaged ? `git diff --cached --numstat "${filePath}"` : `git diff --numstat "${filePath}"`
			const { stdout } = await execAsync(command, { cwd: this.cwd })
			
			if (!stdout.trim()) {
				return { linesAdded: 0, linesRemoved: 0 }
			}

			const [added, removed] = stdout.trim().split("\t")
			return {
				linesAdded: parseInt(added) || 0,
				linesRemoved: parseInt(removed) || 0,
			}
		} catch (error) {
			return { linesAdded: 0, linesRemoved: 0 }
		}
	}

	/**
	 * Get preview data for a specific file
	 */
	public async getFilePreview(filePath: string): Promise<FilePreviewData | undefined> {
		if (!(await this.isGitRepo())) {
			return undefined
		}

		try {
			// Get file status
			const { stdout: statusOutput } = await execAsync(`git status --porcelain "${filePath}"`, { cwd: this.cwd })
			if (!statusOutput.trim()) {
				return undefined
			}

			const status = statusOutput.substring(0, 2).trim()
			const isStaged = status[0] !== " " && status[0] !== "?"

			// Get diff content
			const diffCommand = isStaged ? `git diff --cached "${filePath}"` : `git diff "${filePath}"`
			const { stdout: diffContent } = await execAsync(diffCommand, { cwd: this.cwd })

			// Get original and modified content
			let originalContent = ""
			let modifiedContent = ""

			try {
				const absolutePath = path.resolve(this.cwd, filePath)
				modifiedContent = await fs.readFile(absolutePath, "utf-8")
			} catch (error) {
				// File might be deleted
			}

			try {
				const { stdout } = await execAsync(`git show HEAD:"${filePath}"`, { cwd: this.cwd })
				originalContent = stdout
			} catch (error) {
				// File might be new
			}

			const { linesAdded, linesRemoved } = await this.getLineChanges(filePath, isStaged)

			return {
				filePath,
				originalContent,
				modifiedContent,
				diffContent,
				status,
				linesAdded,
				linesRemoved,
			}
		} catch (error) {
			console.error(`Failed to get preview for ${filePath}:`, error)
			return undefined
		}
	}

	/**
	 * Check if there are any staged changes
	 */
	public async hasStagedChanges(): Promise<boolean> {
		if (!(await this.isGitRepo())) {
			return false
		}

		try {
			const { stdout } = await execAsync("git diff --cached --name-only", { cwd: this.cwd })
			return stdout.trim().length > 0
		} catch (error) {
			return false
		}
	}

	/**
	 * Commit all staged changes
	 */
	public async commitStagedChanges(message: string = "Cubent: Apply changes"): Promise<{ success: boolean; error?: string }> {
		if (!(await this.isGitRepo())) {
			return { success: false, error: "Not a git repository" }
		}

		if (!(await this.hasStagedChanges())) {
			return { success: false, error: "No staged changes to commit" }
		}

		try {
			await execAsync(`git commit -m "${message}"`, { cwd: this.cwd })
			return { success: true }
		} catch (error) {
			return { success: false, error: `Failed to commit: ${error}` }
		}
	}

	/**
	 * Reset all staged changes
	 */
	public async resetStagedChanges(): Promise<{ success: boolean; error?: string }> {
		if (!(await this.isGitRepo())) {
			return { success: false, error: "Not a git repository" }
		}

		try {
			await execAsync("git reset HEAD", { cwd: this.cwd })
			return { success: true }
		} catch (error) {
			return { success: false, error: `Failed to reset: ${error}` }
		}
	}

	/**
	 * Get total statistics for all changes
	 */
	public async getTotalStats(): Promise<{ totalLinesAdded: number; totalLinesRemoved: number; fileCount: number; stagedCount: number }> {
		const changes = await this.getAllChanges()
		
		let totalLinesAdded = 0
		let totalLinesRemoved = 0
		let stagedCount = 0

		for (const change of changes) {
			totalLinesAdded += change.linesAdded
			totalLinesRemoved += change.linesRemoved
			if (change.isStaged) {
				stagedCount++
			}
		}

		return {
			totalLinesAdded,
			totalLinesRemoved,
			fileCount: changes.length,
			stagedCount,
		}
	}
}
